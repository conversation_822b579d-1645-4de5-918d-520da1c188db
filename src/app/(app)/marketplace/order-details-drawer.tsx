'use client';

import { useState } from 'react';

import { TonLogo } from '@/components/TonLogo';
import { Badge } from '@/components/ui/badge';
import { type OrderEntity, UserType } from '@/constants/core.constants';
import {
  getUserIdToFetch,
  getUserLabel,
  useOrderUserInfo,
} from '@/hooks/use-order-user-info';
import { useRootContext } from '@/root-context';
import { executeMarketplaceOrderAction } from '@/utils/order-action-utils';

import {
  OrderDetailsActionButtons,
  OrderDetailsBaseDrawer,
  OrderDetailsDescriptionSection,
  OrderDetailsHeaderSection,
  OrderDetailsImageSection,
  OrderDetailsInfoRow,
  OrderDetailsUserInfoSection,
} from './order-details-drawer/index';

interface OrderDetailsDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order: OrderEntity | null;
  userType?: UserType;
  onOrderAction?: () => void;
}

const isSecondaryMarketOrder = (order: OrderEntity): boolean => {
  return (
    order.status === 'paid' &&
    order.secondaryMarketPrice !== null &&
    order.secondaryMarketPrice !== undefined &&
    order.secondaryMarketPrice > 0
  );
};

export function OrderDetailsDrawer({
  open,
  onOpenChange,
  order,
  userType,
  onOrderAction,
}: OrderDetailsDrawerProps) {
  const { collections } = useRootContext();
  const [actionLoading, setActionLoading] = useState(false);

  const collection = order
    ? collections.find((c) => c.id === order.collectionId) || null
    : null;

  const isSecondary = order ? isSecondaryMarketOrder(order) : false;

  // For secondary market orders, we default to buyer behavior
  const effectiveUserType = userType || UserType.BUYER;

  const userIdToFetch = getUserIdToFetch(
    order?.buyerId,
    order?.sellerId,
    effectiveUserType,
  );
  const { userInfo, loading, handleClose } = useOrderUserInfo({
    userId: userIdToFetch,
    isOpen: open,
  });

  const handleAction = async () => {
    if (!order?.id) return;

    setActionLoading(true);

    let result;
    if (isSecondary) {
      // For secondary market orders, use the secondary market purchase function
      const { makeSecondaryMarketPurchase } = await import('@/api/orders-api');
      try {
        result = await makeSecondaryMarketPurchase(order.id);
      } catch (error) {
        console.error('Error making secondary market purchase:', error);
        result = { success: false };
      }
    } else {
      // For regular orders, use the existing marketplace action
      result = await executeMarketplaceOrderAction(order.id, effectiveUserType);
    }

    if (result.success) {
      onOpenChange(false);
      if (onOrderAction) {
        onOrderAction();
      }
    }
    setActionLoading(false);
  };

  const handleDrawerClose = () => {
    handleClose();
    onOpenChange(false);
  };

  if (!order) return null;

  const getActionPrice = () => {
    return isSecondary ? order.secondaryMarketPrice || 0 : order.price;
  };

  const actionLabel = (
    <>
      {isSecondary ? (
        <>
          Buy &#40;{getActionPrice()} <TonLogo className="-m-2" size={18} />
          <span className="-ml-1 translate-x-[1px]">&#41;</span>
        </>
      ) : effectiveUserType === UserType.BUYER ? (
        <>
          Buy &#40;{order.price} <TonLogo className="-m-2" size={18} />
          <span className="-ml-1 translate-x-[1px]">&#41;</span>
        </>
      ) : (
        <>
          Fulfill &#40;{order.price} <TonLogo className="-m-2" size={18} />
          <span className="-ml-1 translate-x-[1px]">&#41;</span>
        </>
      )}
    </>
  );

  return (
    <OrderDetailsBaseDrawer open={open} onOpenChange={onOpenChange}>
      <OrderDetailsImageSection
        collectionId={order.collectionId}
        collection={collection}
      />

      <OrderDetailsHeaderSection
        {...{
          order,
          collection,
        }}
      />

      <div className="space-y-4">
        <OrderDetailsDescriptionSection collection={collection} />

        <div className="space-y-3">
          {isSecondary ? (
            <>
              <OrderDetailsInfoRow
                label="Primary Price"
                value={
                  <div className="flex items-center gap-1">
                    <span>{order.price}</span>
                    <TonLogo size={16} />
                  </div>
                }
              />

              <OrderDetailsInfoRow
                label="Secondary Price"
                value={
                  <div className="flex items-center gap-1">
                    <span>{order.secondaryMarketPrice || 0}</span>
                    <TonLogo size={16} className="text-[#6ab2f2]" />
                  </div>
                }
              />

              <OrderDetailsInfoRow
                label="Status"
                value={
                  <Badge
                    variant="secondary"
                    className="bg-[#6ab2f2]/20 text-[#6ab2f2] border-[#6ab2f2]/30"
                  >
                    Secondary Market
                  </Badge>
                }
              />

              <OrderDetailsInfoRow
                label="Market Type"
                value={<span>Resale</span>}
                isLast
              />
            </>
          ) : (
            <>
              <OrderDetailsInfoRow
                label="Order Price"
                value={
                  <div className="flex items-center gap-1">
                    <span>{order.price}</span>
                    <TonLogo size={16} />
                  </div>
                }
              />

              <OrderDetailsInfoRow
                label="Status"
                value={
                  <Badge
                    variant="secondary"
                    className="bg-[#6ab2f2]/20 text-[#6ab2f2] border-[#6ab2f2]/30"
                  >
                    {collection?.status || 'Active'}
                  </Badge>
                }
              />

              <OrderDetailsInfoRow
                label="Order Type"
                value={
                  <span className="capitalize">
                    {effectiveUserType === UserType.SELLER
                      ? 'Selling'
                      : 'Buying'}
                  </span>
                }
                isLast
              />
            </>
          )}
        </div>
      </div>

      {userIdToFetch && (
        <OrderDetailsUserInfoSection
          {...{
            userInfo,
            loading,
          }}
          userLabel={isSecondary ? 'Reseller' : getUserLabel(effectiveUserType)}
        />
      )}

      <OrderDetailsActionButtons
        primaryAction={{
          label: actionLabel,
          onClick: handleAction,
          loading: actionLoading,
        }}
        onClose={handleDrawerClose}
        actionLoading={actionLoading}
      />
    </OrderDetailsBaseDrawer>
  );
}
