/* eslint-disable indent */
export interface AppConfig {
  app: {
    environment: string;
    project_id: string;
  };
  telegram: {
    bot_token: string;
    local_bot_token: string;
    api_id: number;
    api_hash: string;
  };
  ton: {
    rpc_url_testnet?: string;
    rpc_url_mainnet?: string;
    marketplace_wallet: string;
    marketplace_wallet_mnemonic: string;
    network: string;
    api_key?: string;
  };
  firebase?: {
    service_account_key?: string;
  };
  url: {
    web_app_url: string;
    webhook_url: string;
  };
}

let cachedConfig: AppConfig | null = null;

export function getConfig(): AppConfig {
  if (cachedConfig) {
    return cachedConfig;
  }

  // For debugging: uncomment the following lines if you need to troubleshoot environment variables
  // console.log("Environment variables available:", {
  //   APP_ENVIRONMENT: process.env.APP_ENVIRONMENT,
  //   TELEGRAM_BOT_TOKEN: process.env.TELEGRAM_BOT_TOKEN ? "***SET***" : "NOT_SET",
  //   TON_MARKETPLACE_WALLET: process.env.TON_MARKETPLACE_WALLET ? "***SET***" : "NOT_SET",
  // });

  // For v2 functions, use environment variables instead of functions.config()
  cachedConfig = {
    app: {
      environment: process.env.APP_ENVIRONMENT ?? "development",
      project_id: process.env.GCLOUD_PROJECT ?? process.env.GCP_PROJECT ?? "",
    },
    telegram: {
      bot_token: process.env.TELEGRAM_BOT_TOKEN ?? "",
      local_bot_token: process.env.TELEGRAM_LOCAL_BOT_TOKEN ?? "",
      api_id: parseInt(process.env.TELEGRAM_API_ID ?? "0"),
      api_hash: process.env.TELEGRAM_API_HASH ?? "",
    },
    ton: {
      rpc_url_testnet: process.env.TON_RPC_URL_TESTNET,
      rpc_url_mainnet: process.env.TON_RPC_URL_MAINNET,
      marketplace_wallet: process.env.TON_MARKETPLACE_WALLET ?? "",
      marketplace_wallet_mnemonic:
        process.env.TON_MARKETPLACE_WALLET_MNEMONIC ?? "",
      network: process.env.TON_NETWORK ?? "mainnet",
      api_key: process.env.TON_API_KEY,
    },
    firebase: {
      service_account_key: process.env.FIREBASE_SERVICE_ACCOUNT_KEY,
    },
    url: {
      web_app_url: process.env.WEB_APP_URL ?? "",
      webhook_url: process.env.WEBHOOK_URL ?? "",
    },
  };

  return cachedConfig;
}

export function getTonRpcUrl(): string {
  const config = getConfig();
  const network = config.ton.network;

  let rpcUrl: string;

  rpcUrl = config.ton.rpc_url_mainnet as string;

  if (!rpcUrl) {
    console.error(`TON RPC URL for ${network} network not configured`);
    throw new Error(`TON RPC URL for ${network} network not configured`);
  }

  // Ensure URL ends with / for proper path concatenation
  return rpcUrl.endsWith("/") ? rpcUrl : `${rpcUrl}/`;
}

export function getMarketplaceWallet(): string {
  const config = getConfig();
  const wallet = config.ton.marketplace_wallet;

  if (!wallet) {
    console.error("TON marketplace wallet not configured");
    throw new Error("TON marketplace wallet not configured");
  }

  return wallet;
}

export function getMarketplaceWalletMnemonic(): string {
  const config = getConfig();
  const mnemonic = config.ton.marketplace_wallet_mnemonic;

  if (!mnemonic) {
    console.error("TON marketplace wallet mnemonic not configured");
    throw new Error("TON marketplace wallet mnemonic not configured");
  }

  return mnemonic;
}

export function getTelegramBotToken(useLocalBotToken?: boolean): string {
  const config = getConfig();
  const token = useLocalBotToken
    ? config.telegram.local_bot_token
    : config.telegram.bot_token;

  if (!token) {
    console.error("Telegram bot token not configured");
    throw new Error("Telegram bot token not configured");
  }

  return token;
}

export function getTelegramApiId(): number {
  const config = getConfig();
  const apiId = config.telegram.api_id;

  if (!apiId || apiId === 0) {
    console.error("Telegram API ID not configured");
    throw new Error("Telegram API ID not configured");
  }

  return apiId;
}

export function getTelegramApiHash(): string {
  const config = getConfig();
  const apiHash = config.telegram.api_hash;

  if (!apiHash) {
    console.error("Telegram API hash not configured");
    throw new Error("Telegram API hash not configured");
  }

  return apiHash;
}

export function isDevelopment(): boolean {
  const config = getConfig();
  return config.app.environment !== "production";
}

export const CORS_CONFIG = isDevelopment()
  ? [
      /^https?:\/\/localhost(:\d+)?$/,
      /^https:\/\/.*\.vercel\.app$/,
      /^https:\/\/.*\.ngrok-free\.app$/,
      /^https:\/\/.*\.us-central1\.run\.app$/,
    ]
  : [getConfig().url.web_app_url, getConfig().url.webhook_url];
