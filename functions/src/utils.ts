import { Timestamp } from "firebase-admin/firestore";

export function roundToTwoDecimals(value: number): number {
  return Math.round(value * 100) / 100;
}

export function safeMultiply(amount: number, multiplier: number): number {
  return roundToTwoDecimals(amount * multiplier);
}

export function safeDivide(amount: number, divisor: number): number {
  if (divisor === 0) {
    console.error("Division by zero error in safeDivide function");
    throw new Error("Division by zero");
  }
  return roundToTwoDecimals(amount / divisor);
}

export function safeAdd(a: number, b: number): number {
  return roundToTwoDecimals(a + b);
}

export function safeSubtract(a: number, b: number): number {
  return roundToTwoDecimals(a - b);
}

export function firebaseTimestampToDate(timestamp: Timestamp): Date {
  return timestamp.toDate();
}

export function formatDateToFirebaseTimestamp(
  date: Date | Timestamp
): Timestamp {
  if (date instanceof Timestamp) {
    return date;
  }
  return Timestamp.fromDate(date);
}

export function generateUniqueId(): string {
  return Date.now().toString() + Math.random().toString(36).substr(2, 9);
}

export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function extractRawTonAddress(address: string): string | null {
  if (!address || address.length < 48) {
    return null;
  }

  // TON addresses have:
  // - 2 character prefix (EQ/UQ)
  // - 44 character base64 encoded address
  // - 3 character checksum
  // We want the middle part without prefix and checksum
  return address.substring(2, address.length - 3);
}

export function isValidTonAddress(address: string): boolean {
  if (!address || typeof address !== "string") {
    return false;
  }

  // Check length (should be 48 characters total)
  if (address.length !== 48) {
    return false;
  }

  // Check prefix (should start with EQ or UQ)
  if (!address.startsWith("EQ") && !address.startsWith("UQ")) {
    return false;
  }

  // Check if the rest is valid base64-like characters
  const addressPart = address.substring(2);
  const base64Regex = /^[A-Za-z0-9+/=_-]+$/;
  return base64Regex.test(addressPart);
}

export function prepareUserDataForSave(userData: any): any {
  const result = { ...userData };

  if (userData.ton_wallet_address) {
    const rawAddress = extractRawTonAddress(userData.ton_wallet_address);
    if (rawAddress) {
      result.raw_ton_wallet_address = rawAddress;
    }
  }

  return result;
}
