import * as admin from "firebase-admin";
import { HttpsError } from "firebase-functions/v2/https";
import { CollectionEntity, OrderEntity, OrderStatus, UserType } from "../types";
import { hasAvailableBalance } from "./balance-service";
import { getAppConfig } from "./fee-service";

import { safeMultiply } from "../utils";

export async function validateCollectionAndFloorPrice(
  db: admin.firestore.Firestore,
  collectionId: string,
  amount: number
): Promise<CollectionEntity> {
  const collectionDoc = await db
    .collection("collections")
    .doc(collectionId)
    .get();

  if (!collectionDoc.exists) {
    throw new HttpsError("not-found", "Collection not found.");
  }

  const collection = collectionDoc.data() as CollectionEntity;

  // Check if collection is active
  if (collection.active === false) {
    throw new HttpsError(
      "failed-precondition",
      "This collection is not active for order creation."
    );
  }

  if (amount < collection?.floorPrice) {
    throw new HttpsError(
      "invalid-argument",
      `Order amount must be at least ${collection?.floorPrice} TON (collection floor price).`
    );
  }

  return collection;
}

export async function validateBalanceAndCalculateLock({
  userId,
  amount,
  userType,
  orderId,
}: {
  userId: string;
  amount: number;
  userType: UserType;
  orderId: string;
}): Promise<{ lockedAmount: number; lockPercentage: number }> {
  const config = await getAppConfig();

  const lockPercentageBPS =
    userType === UserType.BUYER
      ? config?.buyer_lock_percentage
      : config?.seller_lock_percentage;

  // Convert BPS to decimal (divide by 10000)
  const lockPercentage = lockPercentageBPS / 10000;
  const lockedAmount = safeMultiply(amount, lockPercentage);

  const hasBalance = await hasAvailableBalance(userId, lockedAmount);
  if (!hasBalance) {
    throw new HttpsError(
      "failed-precondition",
      `Insufficient balance. You need at least ${lockedAmount} TON available (${
        lockPercentage * 100
      }% of ${amount} TON order amount).`
    );
  }

  return { lockedAmount, lockPercentage };
}

export async function validateOrderCreation(
  db: admin.firestore.Firestore,
  params: {
    userId: string;
    collectionId: string;
    price: number;
    userType: UserType;
  }
): Promise<{
  collection: CollectionEntity;
  lockedAmount: number;
  lockPercentage: number;
}> {
  const { userId, collectionId, price, userType } = params;

  const collection = await validateCollectionAndFloorPrice(
    db,
    collectionId,
    price
  );
  const { lockedAmount, lockPercentage } =
    await validateBalanceAndCalculateLock(userId, price, userType);

  return { collection, lockedAmount, lockPercentage };
}

export async function getAndValidateOrder(
  db: admin.firestore.Firestore,
  orderId: string
): Promise<OrderEntity> {
  const orderDoc = await db.collection("orders").doc(orderId).get();

  if (!orderDoc.exists) {
    throw new HttpsError("not-found", "Order not found.");
  }

  return { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;
}

export function validateOrderAvailableForPurchase(order: OrderEntity): void {
  if (order.status !== OrderStatus.ACTIVE) {
    throw new HttpsError(
      "failed-precondition",
      "Order is not available for purchase."
    );
  }
}

export function validateBuyerPurchaseConstraints(
  order: OrderEntity,
  buyerId: string
): void {
  // Check if order already has a buyer
  if (order.buyerId && order.buyerId !== buyerId) {
    throw new HttpsError("failed-precondition", "Order already has a buyer.");
  }

  // Check if buyer is trying to buy their own order
  if (order.sellerId === buyerId) {
    throw new HttpsError(
      "failed-precondition",
      "You cannot buy your own order."
    );
  }
}

export function validateSellerPurchaseConstraints(
  order: OrderEntity,
  sellerId: string
): void {
  // Check if order already has a seller
  if (order.sellerId && order.sellerId !== sellerId) {
    throw new HttpsError("failed-precondition", "Order already has a seller.");
  }

  // Check if seller is trying to sell to their own order
  if (order.buyerId === sellerId) {
    throw new HttpsError(
      "failed-precondition",
      "You cannot sell to your own order."
    );
  }
}

export async function validateBuyerPurchase(
  db: admin.firestore.Firestore,
  params: {
    userId: string;
    orderId: string;
  }
): Promise<{
  order: OrderEntity;
  lockedAmount: number;
  lockPercentage: number;
}> {
  const { userId, orderId } = params;
  const order = await getAndValidateOrder(db, orderId);

  validateOrderAvailableForPurchase(order);
  validateBuyerPurchaseConstraints(order, userId);

  const { lockedAmount, lockPercentage } =
    await validateBalanceAndCalculateLock(userId, order.price, UserType.BUYER);

  return { order, lockedAmount, lockPercentage };
}

export async function validateSellerPurchase(
  db: admin.firestore.Firestore,
  params: {
    userId: string;
    orderId: string;
  }
): Promise<{
  order: OrderEntity;
  lockedAmount: number;
  lockPercentage: number;
}> {
  const { userId, orderId } = params;
  const order = await getAndValidateOrder(db, orderId);

  validateOrderAvailableForPurchase(order);
  validateSellerPurchaseConstraints(order, userId);

  const { lockedAmount, lockPercentage } =
    await validateBalanceAndCalculateLock(userId, order.price, UserType.SELLER);

  return { order, lockedAmount, lockPercentage };
}
