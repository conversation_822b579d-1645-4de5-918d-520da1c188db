import { onSchedule } from "firebase-functions/v2/scheduler";
import fetch from "node-fetch";
import { BOT_HEALTH_CHECK_ENDPOINT } from "./constants";

export async function checkBotHealth() {
  try {
    console.log("Starting bot health check...");

    const botAppUrl = process.env.BOT_APP_URL;

    if (!botAppUrl) {
      throw new Error("BOT_APP_URL environment variable is not configured");
    }

    const healthCheckUrl = `${botAppUrl}${BOT_HEALTH_CHECK_ENDPOINT}`;
    console.log(`Calling bot health check endpoint: ${healthCheckUrl}`);

    const response = await fetch(healthCheckUrl, {
      method: "GET",
      timeout: 30000, // 30 second timeout
    });

    if (!response.ok) {
      throw new Error(
        `Bot health check failed with status ${response.status}: ${response.statusText}`
      );
    }

    const responseData = await response.json();
    console.log("Bot health check response:", responseData);

    if (responseData.status === "healthy") {
      console.log("✅ Bot health check passed - bot is healthy");
    } else {
      console.warn(
        "⚠️ Bot health check returned unhealthy status:",
        responseData
      );
    }

    return {
      success: true,
      status: responseData.status,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error("❌ Bot health check failed:", error);
    throw error;
  }
}

export const botHealthCheck = onSchedule(
  {
    schedule: "*/15 * * * *", // Every 15 minutes
    timeZone: "UTC",
  },
  async () => {
    try {
      console.log(
        "Bot health check monitor triggered at:",
        new Date().toISOString()
      );
      await checkBotHealth();
      console.log("Bot health check monitor completed successfully");
    } catch (error) {
      console.error("Bot health check monitor failed:", error);
    }
  }
);
